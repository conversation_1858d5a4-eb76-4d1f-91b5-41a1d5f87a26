#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
裁判文书数据整理脚本
根据规则.md的要求，提取和整理裁判文书信息
"""

import pandas as pd
import re
import sys
from datetime import datetime

def extract_trial_level(text):
    """提取审级信息"""
    if not text or pd.isna(text):
        return ""
    
    text = str(text)
    # 一审关键词
    first_trial_keywords = ['一审', '初审', '民初', '刑初', '行初']
    # 二审关键词  
    second_trial_keywords = ['二审', '终审', '民终', '刑终', '行终']
    # 再审关键词
    retrial_keywords = ['再审', '申请再审', '民再', '刑再', '行再']
    
    for keyword in retrial_keywords:
        if keyword in text:
            return "再审"
    
    for keyword in second_trial_keywords:
        if keyword in text:
            return "二审"
            
    for keyword in first_trial_keywords:
        if keyword in text:
            return "一审"
    
    return ""

def extract_plaintiff_defendant(text):
    """提取原告/被告信息"""
    if not text or pd.isna(text):
        return ""
    
    text = str(text)
    # 查找原告和被告信息
    plaintiff_pattern = r'原告[：:]([^，。；\n]+)'
    defendant_pattern = r'被告[：:]([^，。；\n]+)'
    
    plaintiffs = re.findall(plaintiff_pattern, text)
    defendants = re.findall(defendant_pattern, text)
    
    result = []
    if plaintiffs:
        result.append(f"原告：{plaintiffs[0].strip()}")
    if defendants:
        result.append(f"被告：{defendants[0].strip()}")
    
    return "；".join(result)

def extract_year(text):
    """提取年份信息"""
    if not text or pd.isna(text):
        return ""

    text = str(text)
    # 查找年份模式
    year_patterns = [
        r'（(\d{4})）',  # （2021）格式
        r'\((\d{4})\)',  # (2021)格式
        r'(\d{4})年',    # 2021年格式
    ]

    for pattern in year_patterns:
        matches = re.findall(pattern, text)
        if matches:
            year = int(matches[0])
            if 1990 <= year <= 2030:  # 合理年份范围
                return str(year)

    return ""

def extract_litigation_result(text):
    """提取诉讼结果"""
    if not text or pd.isna(text):
        return ""
    
    text = str(text)
    
    # 常见诉讼结果关键词
    result_keywords = {
        "原告撤诉": ["原告撤诉", "申请人撤诉", "撤回起诉", "撤回申请"],
        "驳回全部诉讼请求": ["驳回原告", "驳回申请人", "驳回全部诉讼请求", "驳回诉讼请求"],
        "部分支持": ["部分支持", "部分胜诉"],
        "全部支持": ["支持原告全部诉讼请求", "全部支持"],
        "调解结案": ["调解", "达成调解协议"],
        "移送管辖": ["移送", "管辖权"],
        "中止审理": ["中止审理", "中止诉讼"],
    }
    
    for result, keywords in result_keywords.items():
        for keyword in keywords:
            if keyword in text:
                return result
    
    return ""

def extract_compensation_amount(text):
    """提取赔偿额度"""
    if not text or pd.isna(text):
        return ""
    
    text = str(text)
    
    # 查找金额模式
    amount_patterns = [
        r'赔偿.*?(\d+(?:\.\d+)?)\s*万元',
        r'支付.*?(\d+(?:\.\d+)?)\s*万元', 
        r'给付.*?(\d+(?:\.\d+)?)\s*万元',
        r'赔偿.*?(\d+(?:,\d{3})*(?:\.\d+)?)\s*元',
        r'支付.*?(\d+(?:,\d{3})*(?:\.\d+)?)\s*元',
        r'给付.*?(\d+(?:,\d{3})*(?:\.\d+)?)\s*元',
    ]
    
    for pattern in amount_patterns:
        matches = re.findall(pattern, text)
        if matches:
            amount_str = matches[0].replace(',', '')
            try:
                amount = float(amount_str)
                if '万元' in pattern:
                    return str(int(amount * 10000))
                else:
                    return str(int(amount))
            except ValueError:
                continue
    
    return ""

def extract_legal_basis(text):
    """提取法律依据"""
    if not text or pd.isna(text):
        return ""
    
    text = str(text)
    
    # 查找法律条文
    legal_patterns = [
        r'《([^》]+)》第(\d+)条',
        r'根据《([^》]+)》',
        r'依据《([^》]+)》',
        r'按照《([^》]+)》',
    ]
    
    legal_basis = []
    for pattern in legal_patterns:
        matches = re.findall(pattern, text)
        for match in matches:
            if isinstance(match, tuple):
                if len(match) == 2:
                    legal_basis.append(f"《{match[0]}》第{match[1]}条")
                else:
                    legal_basis.append(f"《{match[0]}》")
            else:
                legal_basis.append(f"《{match}》")
    
    return "；".join(list(set(legal_basis))[:3])  # 最多保留3个，去重

def process_document():
    """处理文档的主函数"""
    try:
        # 读取Excel文件
        print("正在读取Excel文件...")
        df = pd.read_excel('查询结果文档.xlsx')
        print(f"成功读取文件，共{len(df)}条记录")
        
        # 创建备份
        df_backup = df.copy()
        
        # 逐条处理
        for index, row in df.iterrows():
            print(f"\n正在处理第{index + 1}条记录...")
            
            full_text = str(row.get('全文', ''))
            
            # 提取信息
            trial_level = extract_trial_level(full_text)
            plaintiff_defendant = extract_plaintiff_defendant(full_text)
            year = extract_year(full_text)
            litigation_result = extract_litigation_result(full_text)
            compensation_amount = extract_compensation_amount(full_text)
            legal_basis = extract_legal_basis(full_text)
            
            # 更新数据（只更新非空值）
            if trial_level:
                df.at[index, '审级'] = trial_level
            if plaintiff_defendant:
                df.at[index, '原/被告'] = plaintiff_defendant
            if year:
                df.at[index, '年份'] = year
            if litigation_result:
                df.at[index, '诉讼结果'] = litigation_result
            if compensation_amount:
                df.at[index, '赔偿额度'] = compensation_amount
            if legal_basis:
                df.at[index, '法律依据'] = legal_basis
            
            # 显示提取结果
            print(f"  审级: {trial_level}")
            print(f"  原/被告: {plaintiff_defendant[:50]}...")
            print(f"  年份: {year}")
            print(f"  诉讼结果: {litigation_result}")
            print(f"  赔偿额度: {compensation_amount}")
            print(f"  法律依据: {legal_basis[:50]}...")
            
            # 每处理50条保存一次
            if (index + 1) % 50 == 0:
                print(f"\n已处理{index + 1}条，正在保存中间结果...")
                df.to_excel('查询结果文档_处理中.xlsx', index=False)
        
        # 保存最终结果
        print("\n正在保存最终结果...")
        df.to_excel('查询结果文档_已整理.xlsx', index=False)
        print("数据整理完成！结果已保存到 '查询结果文档_已整理.xlsx'")
        
        # 统计信息
        print(f"\n处理统计:")
        print(f"总记录数: {len(df)}")
        print(f"提取到审级的记录: {df['审级'].notna().sum()}")
        print(f"提取到年份的记录: {df['年份'].notna().sum()}")
        print(f"提取到诉讼结果的记录: {df['诉讼结果'].notna().sum()}")
        print(f"提取到赔偿额度的记录: {df['赔偿额度'].notna().sum()}")
        print(f"提取到法律依据的记录: {df['法律依据'].notna().sum()}")
        
    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    process_document()
